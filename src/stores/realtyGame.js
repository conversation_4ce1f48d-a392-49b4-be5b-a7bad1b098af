import { defineStore } from 'pinia'

export const useRealtyGameStore = defineStore('realtyGame', {
  state: () => ({
    properties: [],
    gameTitle: 'Property Price Challenge',
    gameDesc: '',
    gameBgImageUrl: '',
    currentProperty: null,
    isDataLoaded: false,
  }),
  actions: {
    setRealtyGameData(data) {
      this.properties = data.properties || []
      this.gameTitle = data.gameTitle || 'Property Price Challenge'
      this.gameDesc = data.gameDesc || ''
      this.gameBgImageUrl = data.gameBgImageUrl || ''
      this.currentProperty = data.currentProperty || null
      this.isDataLoaded = true
    },
    clearRealtyGameData() {
      this.properties = []
      this.gameTitle = 'Property Price Challenge'
      this.gameDesc = ''
      this.gameBgImageUrl = ''
      this.currentProperty = null
      this.isDataLoaded = false
    },
    getPropertyByIndex(index) {
      return this.properties[index] || null
    },
    getPropertyByUuid(uuid) {
      return this.properties.find((prop) => prop.uuid === uuid) || null
    },
  },
  getters: {
    getProperties: (state) => state.properties,
    getGameTitle: (state) => state.gameTitle,
    getGameDesc: (state) => state.gameDesc,
    getGameBgImageUrl: (state) => state.gameBgImageUrl,
    getCurrentProperty: (state) => state.currentProperty,
    getTotalProperties: (state) => state.properties.length,
    getIsDataLoaded: (state) => state.isDataLoaded,
  },
})
