<template>
  <div class="json-ld-test-page q-pa-md">
    <div class="max-width-md mx-auto">
      <h1 class="text-h4 q-mb-lg">JSON-LD Test Page</h1>

      <p class="text-body1 q-mb-lg">
        This page demonstrates the JSON-LD structured data implementation.
        Check the page source or browser developer tools to see the JSON-LD scripts in the &lt;head&gt; section.
      </p>

      <q-card class="q-mb-lg">
        <q-card-section>
          <h2 class="text-h6 q-mb-md">What's Included:</h2>
          <ul class="q-pl-md">
            <li>Organization schema (HomesToCompare business info)</li>
            <li>WebSite schema (website-level information)</li>
            <li>Service schema (real estate analysis services)</li>
            <li>WebPage schema (this page's metadata)</li>
            <li v-if="showPropertySchema">Property schema (sample property data)</li>
            <li v-if="showGameSchema">Game schema (property price challenge)</li>
          </ul>
        </q-card-section>
      </q-card>

      <div class="row q-col-gutter-md q-mb-lg">
        <div class="col-12 col-md-6">
          <q-btn color="primary"
                 label="Add Sample Property Schema"
                 @click="addPropertySchema"
                 :disable="showPropertySchema"
                 class="full-width" />
        </div>
        <div class="col-12 col-md-6">
          <q-btn color="secondary"
                 label="Add Game Schema"
                 @click="addGameSchema"
                 :disable="showGameSchema"
                 class="full-width" />
        </div>
      </div>

      <div class="row q-col-gutter-md q-mb-lg">
        <div class="col-12 col-md-6">
          <q-btn color="negative"
                 label="Remove Property Schema"
                 @click="removePropertySchema"
                 :disable="!showPropertySchema"
                 class="full-width" />
        </div>
        <div class="col-12 col-md-6">
          <q-btn color="negative"
                 label="Remove Game Schema"
                 @click="removeGameSchema"
                 :disable="!showGameSchema"
                 class="full-width" />
        </div>
      </div>

      <q-card>
        <q-card-section>
          <h2 class="text-h6 q-mb-md">Testing Tools:</h2>
          <p class="text-body2 q-mb-md">Use these tools to validate the JSON-LD:</p>
          <div class="q-gutter-sm">
            <q-btn flat
                   color="primary"
                   label="Google Rich Results Test"
                   @click="openUrl('https://search.google.com/test/rich-results')"
                   icon="open_in_new" />
            <q-btn flat
                   color="primary"
                   label="Schema.org Validator"
                   @click="openUrl('https://validator.schema.org/')"
                   icon="open_in_new" />
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useMeta } from 'quasar'
import useJsonLd from 'src/compose/useJsonLd.js'

const {
  initializeDefaultJsonLd,
  addJsonLd,
  removeJsonLd,
  generatePropertySchema,
  generateGameSessionSchema,
  jsonLdMeta
} = useJsonLd()

// Use Quasar's useMeta to inject JSON-LD scripts
useMeta(() => ({
  script: jsonLdMeta.value
}))

// State
const showPropertySchema = ref(false)
const showGameSchema = ref(false)

// Sample property data for testing
const sampleProperty = {
  title: "Beautiful 3 Bedroom House",
  catchy_title: "Stunning Family Home with Garden",
  description_medium: "A beautiful 3-bedroom family home with modern amenities and a lovely garden. Perfect for families looking for comfort and style.",
  price_sale_current_cents: 45000000, // £450,000
  currency: "GBP",
  count_bedrooms: 3,
  count_bathrooms: 2,
  count_garages: 1,
  street_address: "123 Sample Street",
  city: "London",
  postal_code: "SW1A 1AA",
  country: "GB",
  latitude: 51.5074,
  longitude: -0.1278,
  visible: true,
  constructed_area: 120,
  area_unit: "sqmt",
  year_construction: 2010,
  sale_listing_pics: [
    {
      image_details: {
        url: "https://example.com/property1.jpg"
      }
    },
    {
      image_details: {
        url: "https://example.com/property2.jpg"
      }
    }
  ]
}

// Sample game data
const sampleGameData = {
  title: "Property Price Challenge - Test Game",
  description: "Test your property knowledge with our interactive price guessing game featuring real properties.",
  totalPlayers: 1250
}

// Methods
const addPropertySchema = () => {
  const propertySchema = generatePropertySchema(sampleProperty)
  if (propertySchema) {
    addJsonLd(propertySchema, 'test-property-schema')
    showPropertySchema.value = true
  }
}

const removePropertySchema = () => {
  removeJsonLd('test-property-schema')
  showPropertySchema.value = false
}

const addGameSchema = () => {
  const gameSchema = generateGameSessionSchema(sampleGameData)
  if (gameSchema) {
    addJsonLd(gameSchema, 'test-game-schema')
    showGameSchema.value = true
  }
}

const removeGameSchema = () => {
  removeJsonLd('test-game-schema')
  showGameSchema.value = false
}

const openUrl = (url) => {
  window.open(url, '_blank')
}

// Initialize on mount
onMounted(() => {
  initializeDefaultJsonLd()
})
</script>

<style scoped>
.json-ld-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.max-width-md {
  max-width: 800px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
</style>
