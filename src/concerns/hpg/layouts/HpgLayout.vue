<template>
  <q-layout view="lhh LpR ffr"
            class="hpg-main-layout-outer">
    <!-- <HtocGateMarketingHeader></HtocGateMarketingHeader> -->
    <q-header class="hpg-main-mht-ctr bg-white"
              reveal
              elevated>
      <q-toolbar style=""
                 class="hpg-main-marketing-header-toolbar container max-ctr">
        <q-toolbar-title class="inline-flex items-center">
          <div class="toolbar-site-label-main">
            <a class="ignore-link"
               href="https://housepriceguess.com/">
              <div>
                <span class="color-second"
                      style="color:#665df5">HOUSE</span>
                <span class=""
                      style="color: rgb(10, 0, 131)">PRICE</span>
                <span class="color-second"
                      style="color:#665df5">GUESS</span>
              </div>
            </a>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
                      style="padding-top: 50px">
      <!-- must be careful not to add :key="$route.fullPath" below!! -->
      <router-view @blurbCta="blurbCta"
                   :isPriceGuessOnly="true"
                   @showNotification="showNotification"
                   :whitelabelNameDisplay="whitelabelNameDisplay"
                   :serviceEmail="serviceEmail" />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar ref="bar"
                  position="top"
                  color="accent"
                  size="10px"
                  :skip-hijack="false"
                  :hijack-filter="loadingBarFilterFn" />
    </div>
    <HtocFooter homeUrl="https://housepriceguess.com/"
                :whitelabelNameDisplay="whitelabelNameDisplay"
                :serviceEmail="serviceEmail"></HtocFooter>
    <!-- <HtocMobileFooter v-if="showMobileFooter"></HtocMobileFooter>
    <HtocFooter v-else></HtocFooter>
    <NewAccountEnquiryPrompt :showNewAccEnqPrompt="showNewAccEnqPrompt"
                             :selectedAccountPlan="selectedAccountPlan"
                             @blurbCtaEnd="blurbCtaEnd"></NewAccountEnquiryPrompt> -->
  </q-layout>
</template>
<script>
// import NewAccountEnquiryPrompt from "src/components/customer/NewAccountEnquiryPrompt.vue"
import HtocFooter from "src/concerns/dossiers/components/head-foot/HtocFooter.vue"
// import HtocMobileFooter from "src/apps/hpg-main/components/head-foot/HtocMobileFooter.vue"
import { defineComponent, ref, computed, onMounted } from "vue"
import { useQuasar, useMeta } from "quasar"
import { useRoute } from "vue-router"
import useJsonLd from "src/compose/useJsonLd.js"

export default defineComponent({
  name: "HpgLayout",
  // inject: ["currentUserProvider"],
  components: {
    // // HtocGateMarketingHeader,
    HtocFooter,
    // HtocMobileFooter,
    // NewAccountEnquiryPrompt,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: "free",
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: "<EMAIL>",
    },
    whitelabelNameDisplay: {
      type: String,
      default: "HousePriceGuess",
    }
  },
  computed: {

    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === "rSubdomainRoot") {
        return ""
      } else {
        return "max-ctr"
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    const route = useRoute()

    // Initialize JSON-LD functionality
    const { initializeDefaultJsonLd, updateWebPageSchema, jsonLdMeta } = useJsonLd()

    // Reactive meta data based on current route
    const metaData = computed(() => {
      const routeMeta = route.meta || {}

      // Build meta object from route meta data
      const meta = {
        title: routeMeta.title || 'Property Squares',
        meta: {},
        script: jsonLdMeta.value // Add JSON-LD scripts to meta
      }

      // Add description if available
      if (routeMeta.description) {
        meta.meta.description = { name: 'description', content: routeMeta.description }
      }

      // Add keywords if available
      if (routeMeta.keywords) {
        meta.meta.keywords = { name: 'keywords', content: routeMeta.keywords }
      }

      // Add robots directive if available
      if (routeMeta.robots) {
        meta.meta.robots = { name: 'robots', content: routeMeta.robots }
      }

      // Add Open Graph tags
      if (routeMeta.ogType) {
        meta.meta['og:type'] = { property: 'og:type', content: routeMeta.ogType }
      }

      if (routeMeta.title) {
        meta.meta['og:title'] = { property: 'og:title', content: routeMeta.title }
      }

      if (routeMeta.description) {
        meta.meta['og:description'] = { property: 'og:description', content: routeMeta.description }
      }

      if (routeMeta.ogImage) {
        meta.meta['og:image'] = { property: 'og:image', content: routeMeta.ogImage }
      }

      // Add Twitter Card tags
      if (routeMeta.twitterCard) {
        meta.meta['twitter:card'] = { name: 'twitter:card', content: routeMeta.twitterCard }
      }

      if (routeMeta.title) {
        meta.meta['twitter:title'] = { name: 'twitter:title', content: routeMeta.title }
      }

      if (routeMeta.description) {
        meta.meta['twitter:description'] = { name: 'twitter:description', content: routeMeta.description }
      }

      if (routeMeta.ogImage) {
        meta.meta['twitter:image'] = { name: 'twitter:image', content: routeMeta.ogImage }
      }

      return meta
    })


    // Initialize JSON-LD
    initializeDefaultJsonLd()

    // Update webpage schema based on route
    const routeMeta = route.meta || {}
    updateWebPageSchema({
      title: routeMeta.title,
      description: routeMeta.description,
      keywords: routeMeta.keywords
    })


    // Use the meta data with useMeta
    // useMeta needs to be called AFTER JsonLd has been correctly created!!
    useMeta(() => metaData.value)


    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }

    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes("prop_ev_init") || !url.includes("user"))
        // example (only https://my-service.com/* should trigger)
        // return /^https:\/\/my-service\.com/.test(url)
      },
      // qLang: $q.lang,
    }
  },
  // preFetch({
  //   store,
  //   currentRoute,
  //   previousRoute,
  //   redirect,
  //   ssrContext,
  //   urlPath,
  //   publicPath,
  // }) {
  // },
  // mounted: function () { },
  // watch: {
  //   "currentUserProvider.state.currentUser": {
  //     handler(newCurrentUser, oldVal) {
})
</script>
<style>
.main-layout-hpg-main-2024g {
  /* fix the edit tab disappearing */
  padding-top: 50px;
}
</style>
