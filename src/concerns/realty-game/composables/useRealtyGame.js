import { ref, computed } from 'vue'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export function useRealtyGame() {
  // Reactive state
  const priceGuessData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const userGuesses = ref([])
  const gameResults = ref([])
  const validationRules = ref(null)

  // Function to set realty game data (for SSR integration)
  const setRealtyGameData = (data) => {
    if (data.properties) {
      // Create a mock priceGuessData structure with the provided data
      priceGuessData.value = {
        price_guess_inputs: {
          game_listings: data.properties.map((property) => ({
            listing_details: property,
          })),
          game_title: data.gameTitle || 'Property Price Challenge',
          game_desc: data.gameDesc || '',
          game_bg_image_url: data.gameBgImageUrl || '',
        },
      }
    }
  }

  // API function to fetch price guess data
  const fetchPriceGuessData = async (gameSlug) => {
    isLoading.value = true
    error.value = null
    try {
      let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_inputs`
      if (gameSlug) apiUrl += `/${gameSlug}`
      const response = await axios.get(apiUrl)
      priceGuessData.value = response.data
      userGuesses.value = []
      gameResults.value = []
      validationRules.value =
        priceGuessData.value.price_guess_inputs.guessed_price_validation
      // Also load validation rules
      // try {
      //   validationRules.value = await fetchValidationRules()
      // } catch (validationError) {
      //   console.warn(
      //     'Failed to load validation rules, using defaults:',
      //     validationError
      //   )
      // }

      return response.data
    } catch (err) {
      error.value =
        err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // API function to fetch individual property details by UUID
  const fetchPropertyByUuid = async (propertyUuid) => {
    isLoading.value = true
    error.value = null
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/sale_listings/show/${propertyUuid}`
      const response = await axios.get(apiUrl)
      return response.data
    } catch (err) {
      error.value =
        err.response?.data?.message || 'Failed to fetch property details'
      console.error('Error fetching property details:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  //   # 18 june 2025 - for now only diff b/n above and below
  // # is that listing_display_url is exposed below

  const fetchPriceGuessDataForAdmin = async (gameSlug) => {
    isLoading.value = true
    error.value = null
    try {
      let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_inputs_for_admin`
      if (gameSlug) apiUrl += `/${gameSlug}`
      const response = await axios.get(apiUrl)
      priceGuessData.value = response.data
      userGuesses.value = []
      gameResults.value = []
      validationRules.value =
        priceGuessData.value.price_guess_inputs.guessed_price_validation
      return response.data
    } catch (err) {
      error.value =
        err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // API function to save price estimate
  const savePriceEstimate = async (estimateData, realtyGameSlug) => {
    try {
      // const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_price_games/add_price_estimate/${realtyGameSlug}`
      // don't like the name add_top_level_price_estimate but need to distinguish from above
      // which uses more restrictive assoc b/n scoot and games
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_price_games/add_top_level_price_estimate/${realtyGameSlug}`
      const response = await axios.post(apiUrl, {
        price_estimate: estimateData,
      })
      return response.data
    } catch (err) {
      console.error('Error saving price estimate:', err)
      throw err
    }
  }

  // API function to fetch price estimate comparisons
  const fetchPriceEstimateComparisons = async (listingUuid) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/comparisons/${listingUuid}`
      const response = await axios.get(apiUrl)
      return response.data
    } catch (err) {
      console.error('Error fetching price estimate comparisons:', err)
      throw err
    }
  }

  // // API function to fetch validation rules
  // const fetchValidationRules = async () => {
  //   try {
  //     const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/validation_rules`
  //     const response = await axios.get(apiUrl)
  //     return response.data
  //   } catch (err) {
  //     console.error('Error fetching validation rules:', err)
  //     // Return default rules if API fails
  //     return {
  //       max_percentage_above: 200,
  //       min_percentage_below: 90,
  //       messages: {
  //         too_high: 'Guess is more than 200% too high',
  //         too_low: 'Guess is less than 90% of actual price',
  //         positive_number: 'Please enter a positive number',
  //       },
  //     }
  //   }
  // }

  // Computed properties
  const properties = computed(() => {
    return (
      priceGuessData.value?.price_guess_inputs?.game_listings?.filter(
        (game) => game.listing_details.visible === true
      ) || []
    )
    // return priceGuessData.value?.price_guess_inputs?.game_listings || []
  })

  const adminProperties = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_listings || []
  })

  const realtyGameSummary = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.realty_game_summary || {}
  })

  const gameTitle = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_title || ''
  })

  const gameDefaultCurrency = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.default_game_currency
  })

  const gameDesc = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_desc || ''
  })

  const gameCommunitiesDetails = computed(() => {
    return (
      priceGuessData.value?.price_guess_inputs?.game_communities_details || {}
    )
  })

  const gameBgImageUrl = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_bg_image_url || ''
  })

  const gameStartAt = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_start_at || null
  })

  const gameEndAt = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.game_end_at || null
  })

  const gameSlug = computed(() => {
    return priceGuessData.value?.price_guess_inputs?.realty_game_slug || ''
  })

  const totalProperties = computed(() => {
    return properties.value.length
  })

  // Get property by index
  const getPropertyByIndex = (index) => {
    return properties.value[index] || null
  }

  const getPropertyByUuid = (uuid) => {
    return properties.value.find((prop) => prop.uuid === uuid)
  }

  const totalScore = computed(() => {
    return gameResults.value.reduce((sum, result) => sum + result.score, 0)
  })

  const maxPossibleScore = computed(() => {
    return totalProperties.value * 100
  })

  // Helper functions
  const formatPrice = (priceInCents, currency = 'GBP') => {
    if (!priceInCents) return 'Price not available'

    const amount = priceInCents / 100
    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    } catch (e) {
      return `${currency} ${amount.toFixed(0)}`
    }
  }

  const getPropertyImages = (property) => {
    return property?.sale_listing_pics || []
  }

  const getMainImage = (property) => {
    const images = getPropertyImages(property)
    return images.length > 0 ? images[0] : null
  }

  const validateGuess = (guess, actualPrice) => {
    const errors = []

    if (!guess || guess <= 0) {
      const message =
        validationRules.value?.messages?.positive_number ||
        'Please enter a positive number'
      errors.push(message)
      return { isValid: false, errors }
    }

    // Use backend validation rules or defaults
    const maxPercentageAbove =
      validationRules.value?.max_percentage_above ?? 200
    const maxAllowed = actualPrice * (1 + maxPercentageAbove / 100)

    const minPercentageTooLow =
      validationRules.value?.min_percentage_below ?? 90
    const minAllowed = actualPrice * (1 - minPercentageTooLow / 100)

    if (guess > maxAllowed) {
      const message =
        validationRules.value?.messages?.too_high ||
        `Guess is more than ${maxPercentageAbove}% too high`
      errors.push(message)
    } else if (guess < minAllowed) {
      const message =
        validationRules.value?.messages?.too_low ||
        `Guess is more than ${minPercentageTooLow}% too low`
      errors.push(message)
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  const calculateScore = (guess, actualPrice) => {
    const difference = Math.abs((guess - actualPrice) / actualPrice) * 100

    if (difference <= 5) return 100
    if (difference <= 10) return 90
    if (difference <= 15) return 80
    if (difference <= 20) return 70
    if (difference <= 25) return 60
    if (difference <= 35) return 50
    if (difference <= 50) return 40
    if (difference <= 75) return 30
    if (difference <= 100) return 20
    return 10
  }

  const getFeedbackMessage = (score) => {
    // TODO - get these from the server
    if (score >= 90)
      return 'Excellent! You have a great eye for property values.'
    if (score >= 80)
      return 'Very good! You were very close to the actual price.'
    if (score >= 70)
      return 'Good guess! You have a decent understanding of the market.'
    if (score >= 60) return 'Not too bad! You were reasonably close.'
    if (score >= 50) return "Fair attempt, but there's room for improvement."
    if (score >= 40) return 'Not terrible, but still quite a bit off.'
    if (score >= 30) return 'Quite far off, but keep practicing!'
    return 'Very far from the actual price. Study the market more!'
  }

  const submitGameGuess = async (
    guess,
    userInfo = {},
    property = null,
    propertyIndex = 0
  ) => {
    if (!property) return null

    const actualPrice = property.price_sale_current_cents / 100
    const guessAmount = parseFloat(guess)

    const validation = validateGuess(guessAmount, actualPrice)
    if (!validation.isValid) {
      return { success: false, errors: validation.errors }
    }

    const difference = ((guessAmount - actualPrice) / actualPrice) * 100
    const score = calculateScore(guessAmount, actualPrice)
    const feedback = getFeedbackMessage(score)

    // Prepare estimate data for backend
    const estimateData = {
      guessed_price_in_ui_currency_cents: Math.round(
        userInfo.userGuessInUiCurrency * 100
      ),
      ui_currency: userInfo.uiCurrency,
      score_for_guess: score,
      game_session_string: userInfo.sessionId || null,
      estimated_price_cents: Math.round(guessAmount * 100),
      price_at_time_of_estimate_cents: property.price_sale_current_cents,
      estimate_currency: property.currency || 'GBP',
      estimate_title: `${property.street_address} - ${property.title}`,
      estimate_text: `User guess: ${formatPrice(
        guessAmount * 100,
        property.currency
      )}`,
      estimate_vicinity: property.city,
      estimate_postal_code: property.postal_code,
      estimate_latitude_center: property.latitude,
      estimate_longitude_center: property.longitude,
      estimator_name: userInfo.name || 'Anonymous Player',
      // is_ai_estimate: false,
      is_for_sale_listing: true,
      is_for_rental_listing: false,
      // is_protected: false,
      percentage_above_or_below: difference,
      listing_uuid: property.uuid,
      user_uuid: userInfo.userUuid || null,
      scoot_uuid: userInfo.scootUuid || null,
      estimate_details: {
        game_score: score,
        property_index: propertyIndex,
        game_session_id: userInfo.sessionId || null,
        feedback_message: feedback,
      },
      game_session_id: userInfo.sessionId || null,
    }

    const result = {
      property: property,
      guess: guessAmount,
      actualPrice: actualPrice,
      difference: difference,
      score: score,
      feedback: feedback,
      propertyIndex: propertyIndex,
      estimateData: estimateData,
    }

    try {
      // Save estimate to backend
      const savedEstimate = await savePriceEstimate(
        estimateData,
        userInfo.realtyGameSlug
      )
      result.savedEstimate = savedEstimate

      gameResults.value.push(result)
      userGuesses.value.push(guessAmount)

      // savedEstimate.guessed_price.uuid
      return { success: true, result }
    } catch (error) {
      console.error('Failed to save estimate:', error)
      // Still add to local results even if save fails
      gameResults.value.push(result)
      userGuesses.value.push(guessAmount)

      return {
        success: true,
        result,
        saveError: 'Failed to save estimate to server',
      }
    }
  }

  const resetGame = () => {
    userGuesses.value = []
    gameResults.value = []
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'positive'
    if (score >= 70) return 'primary'
    if (score >= 50) return 'info'
    if (score >= 30) return 'warning'
    return 'negative'
    // if (score >= 80) return 'positive'
    // if (score >= 60) return 'grey'
    // // decided to do away with negative which was red...
    // return 'warning'
  }

  const getPerformanceRating = (totalScore, maxScore) => {
    const percentage = (totalScore / maxScore) * 100

    if (percentage >= 90)
      return { rating: 'Expert', color: 'positive', icon: 'star' }
    if (percentage >= 80)
      return { rating: 'Advanced', color: 'positive', icon: 'trending_up' }
    if (percentage >= 70)
      return { rating: 'Good', color: 'grey', icon: 'thumb_up' }
    if (percentage >= 60)
      return { rating: 'Fair', color: 'grey', icon: 'thumbs_up_down' }
    // decided to do away with negative which was red...
    if (percentage >= 50)
      return { rating: 'Beginner', color: 'warning', icon: 'school' }
    return { rating: 'Novice', color: 'warning', icon: 'help' }
  }

  // Fetch all estimates for comparison summary
  const fetchAllEstimatesForProperties = async () => {
    const allEstimates = []

    for (const property of properties.value) {
      try {
        const estimates = await fetchPriceEstimateComparisons(property.uuid)
        allEstimates.push({
          property: property,
          estimates: estimates,
        })
      } catch (error) {
        console.error(
          `Failed to fetch estimates for property ${property.uuid}:`,
          error
        )
        allEstimates.push({
          property: property,
          estimates: [],
          error: error.message,
        })
      }
    }

    return allEstimates
  }

  // API function to update property visibility
  const updatePropertyVisibility = async (propertyUuid, isVisible) => {
    try {
      // /api_mgmt/v4/realty_games_mgmt/list
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/listing_visibility/${propertyUuid}`
      const response = await axios.patch(apiUrl, {
        sale_listing: {
          visible: isVisible,
        },
      })
      return response.data
    } catch (err) {
      console.error('Error updating property visibility:', err)
      throw err
    }
  }

  // API function to update property title
  const updatePropertyTitle = async (propertyUuid, title) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/sale_listings/${propertyUuid}`
      const response = await axios.patch(apiUrl, {
        sale_listing: {
          title: title,
        },
      })
      return response.data
    } catch (err) {
      console.error('Error updating property title:', err)
      throw err
    }
  }

  // API function to update image visibility
  const updateImageVisibility = async (imageUuid, isHidden) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/photo_visibility/${imageUuid}`
      const response = await axios.patch(apiUrl, {
        sale_listing_pic: {
          flag_is_hidden: isHidden,
        },
      })
      return response.data
    } catch (err) {
      console.error('Error updating image visibility:', err)
      throw err
    }
  }

  // API function to create a new price guess game
  const createPriceGuessGame = async (gameData) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/create_game`
      const response = await axios.post(apiUrl, {
        price_guess_game: gameData,
      })
      return response.data
    } catch (err) {
      console.error('Error creating price guess game:', err)
      throw err
    }
  }

  return {
    adminProperties,
    // State
    priceGuessData,
    isLoading,
    error,
    userGuesses,
    gameResults,

    // Computed
    properties,
    realtyGameSummary,
    gameTitle,
    gameDefaultCurrency,
    gameDesc,
    gameCommunitiesDetails,
    gameBgImageUrl,
    gameStartAt,
    gameEndAt,
    gameSlug,
    totalProperties,
    totalScore,
    maxPossibleScore,

    // Methods
    fetchPriceGuessData,
    fetchPriceGuessDataForAdmin,
    fetchPropertyByUuid,
    // fetchValidationRules,
    savePriceEstimate,
    fetchPriceEstimateComparisons,
    fetchAllEstimatesForProperties,
    getPropertyByIndex,
    getPropertyByUuid,
    formatPrice,
    getPropertyImages,
    getMainImage,
    validateGuess,
    submitGameGuess,
    resetGame,
    getScoreColor,
    getPerformanceRating,
    updatePropertyVisibility,
    updatePropertyTitle,
    updateImageVisibility,
    createPriceGuessGame,
    setRealtyGameData,
  }
}
