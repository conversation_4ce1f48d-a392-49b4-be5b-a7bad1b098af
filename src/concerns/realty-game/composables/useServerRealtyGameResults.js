import { ref, computed } from 'vue'
import axios from 'axios' // Or your preferred HTTP client
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export function useServerRealtyGameResults() {
  const isLoading = ref(false)
  const error = ref(null)
  const results = ref(null)

  const fetchResults = async (gameSessionId) => {
    isLoading.value = true
    error.value = null
    results.value = null
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_price_games/game_session/${gameSessionId}/game_result_calcs`
      const response = await axios.get(apiUrl)
      // (`/api/v1/game_results/${gameSessionId}`)
      results.value = response.data
    } catch (err) {
      console.error('Failed to load game results:', err)
      error.value = err.response?.data?.error || 'An unexpected error occurred.'
    } finally {
      isLoading.value = false
    }
  }

  // Computed properties to easily access data and provide defaults
  const playerResults = computed(() => results.value?.player_results || {})
  const ssGameSession = computed(() => results.value?.game_session || {})
  const comparisonSummary = computed(
    () => results.value?.comparison_summary || []
  )
  const gameBreakdown = computed(() => playerResults.value.game_results || [])

  const formatPrice = (cents, currency) => {
    if (cents === undefined || cents === null) return 'N/A'
    const options = { style: 'currency', currency: currency || 'GBP' }
    return new Intl.NumberFormat('en-GB', options).format(cents / 100)
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'positive'
    if (score >= 70) return 'primary'
    if (score >= 50) return 'info'
    if (score >= 30) return 'warning'
    return 'negative'
  }

  return {
    isLoading,
    error,
    results,
    playerResults,
    ssGameSession,
    comparisonSummary,
    gameBreakdown,
    fetchResults,
    formatPrice, // Kept for formatting inside the table if needed
    getScoreColor, // UI logic, so it stays on the client
  }
}
